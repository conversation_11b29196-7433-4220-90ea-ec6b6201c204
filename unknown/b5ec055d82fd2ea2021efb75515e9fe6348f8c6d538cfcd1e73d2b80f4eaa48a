package com.wexl.retail.email;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.config.OrgSmtpConfigProperties;
import com.wexl.retail.generic.ProfileUtils;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.model.Student;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.organization.ambassador.model.Ambassador;
import com.wexl.retail.organization.dto.OrganizationDto;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.teacher.training.controller.SimpleDataControllerHelper;
import com.wexl.retail.util.StrapiService;
import jakarta.mail.internet.MimeMessage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Slf4j
@Service
public class EmailService {

  private static final String MAIL_CONTENT_TEMPLATE = "content";
  private final String gillcoSlug = "gil923272";
  @Autowired JavaMailSender emailSender;
  @Autowired SpringTemplateEngine templateEngine;
  @Autowired AuthService authService;
  @Autowired UserRepository userRepository;
  @Autowired StrapiService strapiService;
  @Autowired private ProfileUtils profileUtils;
  @Autowired private SimpleDataControllerHelper dataControllerHelper;
  @Autowired private StorageService storageService;
  @Autowired private StudentRepository studentRepository;
  @Autowired private OrgSmtpConfigProperties orgSmtpConfigProperties;

  @Value("${spring.mail.from-email}")
  private String fromEmail;

  @Value("${app.pmp.enabled}")
  private boolean isPmpEnabled;

  @Value("${spring.profiles.active:default}")
  private String envProfile;

  @Value("${app.storageBucket}")
  private String s3BucketName;

  private void sendEmail(Email mail) {
    if (isPmpEnabled) sendEmail(mail, "pmp-email-template");
    else sendEmail(mail, "email-template");
  }

  private void sendEmailForBet(Email mail) {
    sendEmail(mail, "bet-email-template");
  }

  private void sendPasswordResetEmailForBet(Email email) {
    sendEmail(email, "bet-password-reset-template");
  }

  private void sendEmail(Email mail, String emailTemplate) {
    if (profileUtils.isTest()) {
      log.info("Don't send mails in test mode");
      return;
    }
    try {
      var message = emailSender.createMimeMessage();
      var helper =
          new MimeMessageHelper(
              message,
              MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
              StandardCharsets.UTF_8.name());
      log.info("Before Sending email to: " + mail.getMailTo() + " Subject: " + mail.getSubject());
      helper.setTo(mail.getMailTo());
      if (mail.getMailCc() != null && !mail.getMailCc().equals("")) {
        helper.setCc(mail.getMailCc());
      }
      var context = new Context();
      context.setVariables(mail.getProps());
      String html = templateEngine.process(emailTemplate, context);
      helper.setText(html, true);
      helper.setSubject(mail.getSubject());
      helper.setFrom(fromEmail);
      emailSender.send(message);
      log.info("Email sent!");
    } catch (Exception e) {
      log.error("Email error message: " + e.getMessage(), e);
    }
  }

  public void sendEmailAfterOtp(String firstName, String lastName, String emailId, String otp) {
    var email = Email.builder().mailTo(emailId).subject("WeXL Authentication").build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", firstName + " " + lastName);
    model.put(
        MAIL_CONTENT_TEMPLATE,
        "<p>"
            + otp
            + " is user OTP (One Time Password) "
            + "to authenticate your login to WeXL. "
            + "Your OTP is valid for the next 30 minutes.</p>");
    email.setProps(model);
    sendEmail(email);
  }

  public void sendEmailAfterTeacherRegistration(String firstName, String lastName, String emailId) {
    var email =
        Email.builder()
            .mailTo(emailId)
            .subject("Welcome Aboard Partner! Congratulations!!!")
            .build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", firstName + " " + lastName);
    model.put(
        MAIL_CONTENT_TEMPLATE,
        ""
            + "<p>Congratulations! Thank you for registering with WeXL.</p>"
            + "<p>We are thrilled to have partnered with you. "
            + "A teacher’s influence on students can never be "
            + "truly measured; it can only be seen and felt in "
            + "the happy faces of their students. Together we can "
            + "create a safe and healthy learning zone for students "
            + "and help them soar high in the limitless sky of opportunities.</p> "
            + "<p>You can access WeXL with your email <b>"
            + emailId
            + "</b> as your login ID. </p>"
            + "<p>If you don't remember your password, "
            + "click on the Forgot Password link on the "
            + "login page and we'll assist you on every step.</p>"
            + "<p>Happy Teaching!"
            + "</p>");
    email.setProps(model);
    sendEmail(email);
  }

  public void sendSkilliomaSignupEmail(
      String firstName, String lastName, String emailId, String password, String username) {
    var email =
        Email.builder()
            .mailTo(emailId)
            .subject(
                "Thank you for Enrolling in Skillioma National Online Merit Scholarship Test 2023")
            .build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", firstName + " " + lastName);
    model.put("username", username);
    model.put("password", password);
    email.setProps(model);
    sendEmail(email, "scholarship-template");
  }

  public void sendEmailOtp(String emailId, String otp) {
    var email =
        Email.builder()
            .mailTo(emailId)
            .subject("%s Email Verification".formatted(getEnvironment()))
            .build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", "User");
    model.put(
        MAIL_CONTENT_TEMPLATE,
        "<p>%s  is user OTP (One Time Password)  to verify your email to %s. Your OTP is valid for the next 30 minutes.</p>"
            .formatted(otp, getEnvironment()));
    email.setProps(model);
    sendEmail(email);
  }

  private String getEnvironment() {
    if (envProfile.contains("pmp")) {
      return "PMP";
    }
    return "WeXL";
  }

  public void sendEmailForMaharshiTeacher(
      String fullName,
      String emailId,
      String password,
      String username,
      String principalName,
      String institutionName,
      String orgSlug) {
    String link = "https://console.wexledu.com/#/login";
    String studentLink = "https://console.wexledu.com/#/org-signup/student/" + orgSlug;
    var email = Email.builder().mailTo(emailId).subject("Thank you for Registering").build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", fullName);
    model.put("Institution Name", institutionName);
    model.put("Principal Name", principalName);
    model.put("username", username);
    model.put("password", password);
    model.put("link", link);
    model.put("studentRegistrationLink", studentLink);
    model.put(
        MAIL_CONTENT_TEMPLATE,
        "<p><strong>Institution Name: "
            + institutionName
            + "</strong></p>"
            + "<p><strong>Principal Name: "
            + principalName
            + "</strong></p>"
            + "<p>You can get access to the website using the following credentials: </p>"
            + "<p>username : "
            + username
            + "</p>"
            + "<p>password : "
            + password
            + "</p>"
            + "<p>Chairman Link : "
            + link
            + "</p>"
            + "<p>Student Registration Link : "
            + studentLink
            + "<p>If you have trouble logging in or forgot your password, please click on the Forgot "
            + "Password link on the Login Page and we "
            + "will assist you from there ! </p><p>Welcome Onboard !"
            + "</p>");
    email.setProps(model);
    sendEmail(email);
  }

  public void sendEmailForMaharshiStudent(
      String fullName, String emailId, String password, String username) {
    String link = "https://console.wexledu.com/#/login";
    var email = Email.builder().mailTo(emailId).subject("Thank you for Registering").build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", fullName);
    model.put("username", username);
    model.put("password", password);
    model.put("link", link);
    model.put(
        MAIL_CONTENT_TEMPLATE,
        "<p>You can get access to the website using the following credentials: </p>"
            + "<p>username : "
            + username
            + "</p>"
            + "<p>password : "
            + password
            + "</p>"
            + "<p>Link : "
            + link
            + "</p>"
            + "<p>If you have trouble logging in or forgot your password, please click on the Forgot "
            + "Password link on the Login Page and we "
            + "will assist you from there ! </p><p>Welcome Onboard !"
            + "</p>");
    email.setProps(model);
    sendEmail(email);
  }

  public void sendEmailAfterBetRegistration(String link, String emailId) {
    var email = Email.builder().mailTo(emailId).subject("Activate Your Account!!!").build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", emailId);
    model.put("link", link);
    email.setProps(model);
    sendEmailForBet(email);
  }

  public void sendBetPasswordResetLink(String link, String emailId) {
    var email = Email.builder().mailTo(emailId).subject("Password Reset Request").build();

    Map<String, Object> model = new HashMap<>();
    model.put("name", emailId);
    model.put("link", link);

    model.put(
        MAIL_CONTENT_TEMPLATE,
        "<p>Hello,</p>"
            + "<p>We received a request to reset the password for the email address: "
            + emailId
            + ".</p>"
            + "<p>To reset your password, please click the link below:</p>"
            + "<p><a href='"
            + link
            + "' style='color:blue;text-decoration:underline;'>Reset Password</a></p>"
            + "<p>This link will expire in 1 hour. If you did not request a password reset, please ignore this email.</p>"
            + "<p>Best regards,</p>"
            + "<p>Your Support Team</p>");
    email.setProps(model);
    sendPasswordResetEmailForBet(email);
  }

  public void sendCommerceStudentSignupEmail(
      String fullName,
      String emailId,
      String password,
      String username,
      String androidAppUrl,
      String webAppUrl) {
    var email = Email.builder().mailTo(emailId).subject("Thank you for Registering").build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", fullName);
    model.put("username", username);
    model.put("password", password);
    model.put(
        MAIL_CONTENT_TEMPLATE,
        "<p>You can get access to the app using the following credentials: </p>"
            + "<p>username : "
            + username
            + "</p>"
            + "<p>password : "
            + password
            + "</p>"
            + "<p>android app : "
            + androidAppUrl
            + "</p>"
            + "<p>web: "
            + webAppUrl
            + "</p>"
            + "<p>If you have trouble logging in or forgot your password, please click on the Forgot "
            + "Password link on the Login Page and we "
            + "will assist you from there ! </p><p>Welcome Onboard !"
            + "</p>");
    email.setProps(model);
    sendEmail(email);
  }

  public void sendPmpStudentSignupEmail(
      String fullName,
      String emailId,
      String password,
      String username,
      String androidAppUrl,
      String webAppUrl) {
    var email = Email.builder().mailTo(emailId).subject("Thank you for Registering").build();
    Map<String, Object> model = new HashMap<>();
    model.put("name", fullName);
    model.put("username", username);
    model.put("password", password);
    model.put(
        MAIL_CONTENT_TEMPLATE,
        "<p>You can get access to the app using the following credentials: </p>"
            + "<p>username : "
            + username
            + "</p>"
            + "<p>password : "
            + password
            + "</p>"
            + "<p>android app : "
            + androidAppUrl
            + "</p>"
            + "<p>web: "
            + webAppUrl
            + "</p>"
            + "<p>If you have trouble logging in or forgot your password, please click on the Forgot "
            + "Password link on the Login Page and we "
            + "will assist you from there ! </p><p>Welcome Onboard !"
            + "</p>");
    email.setProps(model);
    sendEmail(email, "pmp-email-template");
  }

  public void sendAmbassadorSignupEmailWithAttachment(
      Ambassador ambassador, OrganizationDto.SignupRequest request, String defaultStoreUrl) {
    MimeMessage message = emailSender.createMimeMessage();

    MimeMessageHelper helper;
    try {
      helper =
          new MimeMessageHelper(
              message,
              MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
              StandardCharsets.UTF_8.name());

      Map<String, Object> model = new HashMap<>();
      model.put("name", request.name() + " " + "institute");
      model.put(
          MAIL_CONTENT_TEMPLATE,
              """
                            <p>We're delighted to welcome you to the WeXL Ambassador Program for our English Language Program!</p>
                                                             <p>Your unique Ambassador Code for your institute is: <strong>%s</strong></p>
                                                                     <p><strong>Username: </strong>%s</p>
                                                                     <p> <strong>Password: </strong>%s</p>
                                                              <p> <strong>Product Link:</strong><a href="%s">English Language Program</a></p>
                                                                     <p>With our Ambassador Program, there are numerous benefits:</p>
                                                             <p><strong>Significant Price Discounts: </strong> Your students can enjoy reduced prices when enrolling in our English Language Program using your Ambassador Code.</p>\s


                                                             <p><strong>Gratitude Pay: </strong> Receive recognition for your efforts in student enrollment with gratitude pay. </p>
                                                             <p><strong>Access to Training and Certification Programs:</strong> As part of the program, you'll have opportunities to participate in training and certification programs, announced periodically. </p>
                                                             <p><strong>Teaching and Mentoring Opportunities:</strong> Stay informed about teaching and mentoring opportunities with in our network to further your professional development and engagement.</p>

                                                             <p>Our English Language Program offers a comprehensive approach to improving students' language skills, covering listening, speaking, reading, vocabulary, and grammar. Enhanced with AI speech analysis, students gain valuable insights into their areas for improvement, facilitating a more effective learning journey.</p>

                                                             <p>To enroll your students in this program, simply share your Ambassador Code with them, and they can register themselves under your organization.</P>

                                                             <p><strong>Confidential Information:</strong>

                                                             <ul>
                                                             <li>Up to 40%% of the net student pay will be credited to your account based on the number of students subscribed with your ambassador code.</li>
                                                             <li>Please provide your bank account details to the below-mentioned email address for payment processing. For any further clarification, please reach out to the following contacts i.e. support@wexledu.<NAME_EMAIL>. </li>
                                                             </ul>

                                                             <p>We're excited about the opportunity to collaborate with you and look forward to a successful partnership! </p>

                                                             <p>Best regards,</p>
                                                             <p>Team WeXL</p>

                                                             <p> <strong>Note:</strong> This is a system generated email, please do not reply. You can contact WeXL Support team through https://www.wexledu.com , the mail id mentioned or WeXL apps.</p>
                      """
              .formatted(
                  ambassador.getCode(), request.email(), request.password(), defaultStoreUrl));

      var email =
          Email.builder()
              .mailTo(ambassador.getEmail())
              .subject("Welcome to WeXL Ambassador Program!")
              .build();
      email.setProps(model);
      var context = new Context();
      context.setVariables(email.getProps());
      helper.setSubject(email.getSubject());
      helper.setTo(ambassador.getEmail());
      helper.setText((String) model.get(MAIL_CONTENT_TEMPLATE), true);
      helper.setText(templateEngine.process("email-template", context), true);
      helper.setFrom(fromEmail);
      addAttachmentIfExists(helper);
      emailSender.send(message);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private void addAttachmentIfExists(MimeMessageHelper helper) {

    var fileNamesMap =
        Map.of(
            "student-benefits.pdf",
            "Benefits to Students.pdf",
            "parents-elp.pdf",
            "Parents ELP.pdf",
            "exciting-news-for-parents.pdf",
            "\uD83D\uDE80 Exciting News for Parents! \uD83D\uDE80.pdf");

    fileNamesMap.forEach(
        (fileName, displayName) -> {
          try {
            Optional<File> optionalFile = dataControllerHelper.getFileInLocaleFolder(fileName);
            if (optionalFile.isPresent()) {
              var attachmentData = FileUtils.readFileToByteArray(optionalFile.get());
              ByteArrayResource resource = new ByteArrayResource(attachmentData);
              helper.addAttachment(
                  displayName,
                  resource,
                  Files.probeContentType(Paths.get(optionalFile.get().getName())));
            }
          } catch (Exception e) {
            log.error("Error to add elp brochure attachment", e);
          }
        });
  }

  private JavaMailSender getMailSenderForOrg(String orgSlug) {
    if (orgSmtpConfigProperties != null && orgSmtpConfigProperties.getSmtpConfig() != null) {
      OrgSmtpConfigProperties.SmtpConfig smtpConfig =
          orgSmtpConfigProperties.getSmtpConfig().get(orgSlug);
      if (smtpConfig != null) {
        JavaMailSenderImpl sender = new JavaMailSenderImpl();
        sender.setHost(smtpConfig.getHost());
        sender.setPort(smtpConfig.getPort());
        sender.setUsername(smtpConfig.getUsername());
        sender.setPassword(smtpConfig.getPassword());
        Properties props = sender.getJavaMailProperties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        return sender;
      }
    }
    return emailSender;
  }

  private String getFromEmailForOrg(String orgSlug) {
    if (orgSmtpConfigProperties != null && orgSmtpConfigProperties.getSmtpConfig() != null) {
      OrgSmtpConfigProperties.SmtpConfig smtpConfig =
          orgSmtpConfigProperties.getSmtpConfig().get(orgSlug);
      if (smtpConfig != null && smtpConfig.getFromEmail() != null) {
        return smtpConfig.getFromEmail();
      }
    }
    return fromEmail;
  }

  public void sendEmailNotification(
      Organization organization,
      NotificationDto.EmailTo emailRecipient,
      MessageTemplate messageTemplate) {
    String orgSlug = organization.getSlug();
    JavaMailSender sender = getMailSenderForOrg(orgSlug);
    String from = getFromEmailForOrg(orgSlug);
    try {
      Map<String, Object> model = new HashMap<>();
      model.put("name", emailRecipient.name());
      model.put("organizationName", organization.getName());
      String content =
              """
                <p>%s</p>
                """
              .formatted(messageTemplate.getMessage());
      model.put(MAIL_CONTENT_TEMPLATE, content);

      var message = sender.createMimeMessage();
      var helper =
          new MimeMessageHelper(
              message,
              MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
              StandardCharsets.UTF_8.name());

      helper.setTo(emailRecipient.email());
      helper.setSubject("Email Notification");
      helper.setFrom(from);

      if (messageTemplate.getAttachment() != null) {
        try {
          File file = storageService.downloadFile(messageTemplate.getAttachment(), s3BucketName);
          String fileName = file.getName();

          byte[] data = Files.readAllBytes(file.toPath());

          String contentType =
              URLConnection.guessContentTypeFromStream(new ByteArrayInputStream(data));
          if (contentType == null) {
            contentType = "application/octet-stream";
          }

          ByteArrayResource resource = new ByteArrayResource(data);
          helper.addAttachment(fileName, resource, contentType);

        } catch (FileNotFoundException e) {
          log.warn("Attachment file not found: {}", messageTemplate.getAttachment(), e);
        }
      }

      var context = new Context();
      context.setVariables(model);
      String html = templateEngine.process("email-format", context);

      helper.setText(html, true);

      log.info(
          "Before sending email to: {}, Subject: {}", emailRecipient.email(), "Email Notification");
      sender.send(message);
      log.info("Email sent!");

    } catch (Exception e) {
      log.error("Email error message: " + e.getMessage(), e);
    }
  }

  public void sendBirthdayEmailNotification(
      Organization organization,
      NotificationDto.EmailTo recipient,
      MessageTemplate messageTemplate) {

    try {
      String orgSlug = organization.getSlug();
      JavaMailSender sender = getMailSenderForOrg(orgSlug);
      String from = getFromEmailForOrg(orgSlug);
      Map<String, Object> model = new HashMap<>();
      model.put("name", recipient.name());
      model.put("organizationName", organization.getName());

      String content =
              """
        <p>%s</p>

        <p>
        Warm regards,<br>
        %s.
        </p>
        """
              .formatted(messageTemplate.getMessage(), organization.getName());

      model.put(MAIL_CONTENT_TEMPLATE, content);

      var message = sender.createMimeMessage();
      var helper =
          new MimeMessageHelper(
              message,
              MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
              StandardCharsets.UTF_8.name());

      helper.setTo(recipient.email());
      helper.setSubject("Wishing You a Very Happy Birthday, %s! 🎉".formatted(recipient.name()));
      helper.setFrom(from);

      var context = new Context();
      context.setVariables(model);
      String html = templateEngine.process("email-template", context);

      helper.setText(html, true);

      log.info("Sending birthday email to: {}", recipient.email());
      sender.send(message);
      log.info("Birthday email sent!");

    } catch (Exception e) {
      log.error("Error sending birthday email to: {}", recipient.email(), e);
    }
  }

  public void sendFeeDueEmailNotification(
      Organization organization,
      NotificationDto.StudentEmailRecipient recipient,
      MessageTemplate messageTemplate) {
    Optional<Student> studentOpt = studentRepository.findById(recipient.studentId());
    String sectionName = studentOpt.map(Student::getSection).map(Section::getName).orElse(null);

    String contactBlock = "";
    if (gillcoSlug.equals(organization.getSlug())) {
      contactBlock =
          """
            <p>Mr.Lucky: +91 81969 60097<br>
            Mr.Anil: +91 84370 14897</p>
            """;
    }

    try {
      String orgSlug = organization.getSlug();
      JavaMailSender sender = getMailSenderForOrg(orgSlug);
      String from = getFromEmailForOrg(orgSlug);
      Map<String, Object> model = new HashMap<>();
      model.put("name", "Parent");
      model.put("organizationName", organization.getName());

      String content =
              """
        <p>This is a gentle reminder that the fee for your ward, %s, studying in %s, is overdue for the quarter [1].</p>

        <p>We kindly request you to clear the pending dues at the earliest to avoid the late fee charges as per school rule.</strong>.</p>

        <p>For any queries, please feel free to contact the Accounts Department during school hours:</p>

        %s
        <p>
        Best regards,<br>
        Accounts Department,<br>
        %s.
        </p>
        """
              .formatted(
                  recipient.emailTo().name(), sectionName, contactBlock, organization.getName());

      model.put(MAIL_CONTENT_TEMPLATE, content);

      var message = sender.createMimeMessage();
      var helper =
          new MimeMessageHelper(
              message,
              MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
              StandardCharsets.UTF_8.name());

      helper.setTo(recipient.emailTo().email());
      helper.setSubject("Fee Payment Reminder - %s".formatted(organization.getName()));
      helper.setFrom(from);

      var context = new Context();
      context.setVariables(model);
      String html = templateEngine.process("email-template", context);

      helper.setText(html, true);

      log.info("Sending fee due email to: {}", recipient.emailTo().email());
      sender.send(message);
      log.info("Fee due email sent to: {}", recipient.emailTo().email());

    } catch (Exception e) {
      log.error("Error sending fee due email to: {}", recipient.emailTo().email(), e);
    }
  }
}
