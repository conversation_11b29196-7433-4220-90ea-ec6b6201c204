package com.wexl.retail.student.medical;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.time.LocalDate;

@AllArgsConstructor
@Entity
@Data
@Table(name = "medical_history")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MedicalHistory extends Model {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "medical_history_id_seq")
    @SequenceGenerator(name = "medical_history_id_seq", allocationSize = 1)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    private Student student;

    @Column(name = "student_name")
    private String studentName;

    @Column(name = "class_section")
    private String classSection;

    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @Column(name = "blood_group")
    private String bloodGroup;

    @Column(name = "height_cm")
    private String height;

    @Column(name = "weight_kg")
    private String weight;

    @Column(name = "allergies", columnDefinition = "TEXT")
    private String allergies;

    @Column(name = "medical_conditions", columnDefinition = "TEXT")
    private String medicalConditions;

    @Column(name = "medications", columnDefinition = "TEXT")
    private String medications;

    @Column(name = "emergency_contact_name")
    private String emergencyContactName;

    @Column(name = "emergency_contact_phone")
    private String emergencyContactPhone;

    @Column(name = "emergency_contact_relationship")
    private String emergencyContactRelationship;

    @Column(name = "doctor_name")
    private String doctorName;

    @Column(name = "doctor_phone")
    private String doctorPhone;

    @Column(name = "insurance_provider")
    private String insuranceProvider;

    @Column(name = "insurance_policy_number")
    private String insurancePolicyNumber;

    @Column(name = "vaccination_records", columnDefinition = "TEXT")
    private String vaccinationRecords;

    @Column(name = "special_dietary_requirements", columnDefinition = "TEXT")
    private String specialDietaryRequirements;

    @Column(name = "physical_limitations", columnDefinition = "TEXT")
    private String physicalLimitations;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;
}
