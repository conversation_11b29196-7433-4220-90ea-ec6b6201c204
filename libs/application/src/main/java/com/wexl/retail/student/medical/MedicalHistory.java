package com.wexl.retail.student.medical;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Entity
@Data
@Table(name = "medical_history")
@NoArgsConstructor
public class MedicalHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "exam_id_seq")
    @SequenceGenerator(name = "exam_id_seq", allocationSize = 1)
    private long id;

    private Long student;
    private String name;
    private String Class&Section;
    private int DOB;
    private String BloodGroup;
    private String Height;
    private String Weight;

}
