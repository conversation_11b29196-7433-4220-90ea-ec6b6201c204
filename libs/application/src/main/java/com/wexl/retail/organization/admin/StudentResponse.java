package com.wexl.retail.organization.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.dto.GlobalProfileDto;
import com.wexl.retail.guardian.dto.GuardianDto;
import com.wexl.retail.model.Gender;
import com.wexl.retail.util.Status;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StudentResponse {
  private Long id;
  private String userName;
  private String firstName;
  private String lastName;
  private Gender gender;
  private String email;
  private String rollNumber;
  private String schoolName;
  private String organization;

  @JsonProperty("grade")
  private String gradeSlug;

  @JsonProperty("grade_name")
  private String gradeName;

  @JsonProperty("admission_number")
  private String admissionNumber;

  @JsonProperty("grade_id")
  private int gradeId;

  @JsonProperty("board")
  private String boardSlug;

  @JsonProperty("academicYear")
  private String academicYearSlug;

  private String parentFirstName;
  private String parentLastName;
  private String parentEmail;
  private String parentMobileNumber;
  private Status isDisconnected;
  private String section;
  private String guid;
  private boolean isParentDataAvailable;

  private Long lastLoginTime;

  @JsonProperty("country_code")
  private String countryCode;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("org_name")
  private String orgName;

  @JsonProperty("mobile_number")
  private String mobileNumber;

  @JsonProperty("guardian_details")
  private List<GuardianDto.Response> guardianDetails;

  @JsonProperty("role_template")
  private GlobalProfileDto.RoleTemplateResponse roleTemplates;

  @JsonProperty("class_roll_number")
  private String classRollNumber;

  @JsonProperty("board_name")
  private String boardName;

  @JsonProperty("is_fee_paid")
  private Boolean isFeePaid;
}
