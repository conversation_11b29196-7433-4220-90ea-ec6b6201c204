package com.wexl.retail.staff.service;

import com.wexl.retail.globalprofile.dto.GlobalProfileDto;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.globalprofile.service.RoleTemplateService;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserVerificationStatus;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.staff.dto.StaffDto;
import com.wexl.retail.staff.model.Department;
import com.wexl.retail.staff.model.Designation;
import com.wexl.retail.staff.model.Staff;
import com.wexl.retail.staff.repository.DepartmentRepository;
import com.wexl.retail.staff.repository.DesignationRepository;
import com.wexl.retail.staff.repository.StaffRepository;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StaffService {

  private final StaffRepository staffRepository;
  private final DesignationRepository designationRepository;
  private final DepartmentRepository departmentRepository;
  private final UserRepository userRepository;
  private final PasswordEncoder passwordEncoder;
  private final RoleTemplateService roleTemplateService;

  public StaffDto.StaffResponse createStaff(StaffDto.StaffRequest request, String orgSlug) {
    Optional<User> existingUser = userRepository.findUserByEmail(request.email());
    if (existingUser.isPresent()) {
      throw new IllegalArgumentException("User with email " + request.email() + " already exists");
    }
    String authUserId = UUID.randomUUID().toString();
    String userName = request.email();
    List<RoleTemplate> roleTemplates = new ArrayList<>();
    request
        .role()
        .forEach(role -> roleTemplates.add(roleTemplateService.getRoleTemplateById(role)));
    User user = new User();
    user.setAuthUserId(authUserId);
    user.setUserName(userName);
    user.setFirstName(request.firstName());
    user.setLastName(request.lastName());
    user.setEmail(request.email());
    user.setMobileNumber(request.mobileNumber());
    user.setOrganization(orgSlug);
    user.setEmailVerified(true);
    user.setMobileVerified(true);
    user.setGender(request.gender());
    user.setVerificationStatus(UserVerificationStatus.VERIFIED);
    user.setPassword(passwordEncoder.encode(request.password()));
    User savedUser = userRepository.save(user);
    Staff staff =
        Staff.builder()
            .role(roleTemplates)
            .joiningDate(request.joiningDate())
            .user(savedUser)
            .build();

    if (request.departmentId() != null) {
      Department department =
          departmentRepository
              .findById(request.departmentId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Department not found with id: " + request.departmentId()));
      staff.setDepartment(department);
    }
    if (request.designationId() != null) {
      Designation designation =
          designationRepository
              .findById(request.designationId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Designation not found with id: " + request.designationId()));
      staff.setDesignation(designation);
    }

    Staff savedStaff = staffRepository.save(staff);
    return mapToStaffResponse(savedStaff);
  }

  public StaffDto.StaffResponse getStaffById(Long id, String orgSlug) {
    Staff staff =
        staffRepository
            .findById(id)
            .orElseThrow(() -> new IllegalArgumentException("Staff not found with id: " + id));
    return mapToStaffResponse(staff);
  }

  public List<StaffDto.StaffResponse> getAllStaff(String orgSlug) {
    return staffRepository.getStaffByOrg(orgSlug).stream()
        .map(this::mapToStaffResponse)
        .collect(Collectors.toList());
  }

  public StaffDto.StaffResponse updateStaff(Long id, StaffDto.StaffRequest request) {
    Staff staff =
        staffRepository
            .findById(id)
            .orElseThrow(() -> new IllegalArgumentException("Staff not found with id: " + id));
    List<RoleTemplate> roleTemplates = new ArrayList<>();
    request
        .role()
        .forEach(role -> roleTemplates.add(roleTemplateService.getRoleTemplateById(role)));
    staff.setRole(roleTemplates);
    staff.setJoiningDate(request.joiningDate());
    if (request.departmentId() != null) {
      Department department =
          departmentRepository
              .findById(request.departmentId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Department not found with id: " + request.departmentId()));
      staff.setDepartment(department);
    } else {
      staff.setDepartment(null);
    }
    if (request.designationId() != null) {
      Designation designation =
          designationRepository
              .findById(request.designationId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Designation not found with id: " + request.designationId()));
      staff.setDesignation(designation);
    } else {
      staff.setDesignation(null);
    }

    if (staff.getUser() != null) {
      User user = staff.getUser();
      user.setFirstName(request.firstName());
      user.setLastName(request.lastName());
      user.setEmail(request.email());
      user.setMobileNumber(request.mobileNumber());
      user.setGender(request.gender());
      userRepository.save(user);
    }
    Staff updatedStaff = staffRepository.save(staff);
    return mapToStaffResponse(updatedStaff);
  }

  public void deleteStaff(Long id) {
    Staff staff =
        staffRepository
            .findById(id)
            .orElseThrow(() -> new IllegalArgumentException("Staff not found with id: " + id));
    if (staff.getUser() != null) {
      User user = staff.getUser();
      user.setIsDeleted(true);
      userRepository.save(user);
    }
    staffRepository.deleteById(id);
  }

  private StaffDto.StaffResponse mapToStaffResponse(Staff staff) {
    var user = staff.getUser();
    List<GlobalProfileDto.RoleTemplateResponse> roleTemplates = new ArrayList<>();
    staff.getRole().forEach(r -> roleTemplates.add(roleTemplateService.getRoleTemplates(r)));
    return StaffDto.StaffResponse.builder()
        .id(staff.getId())
        .firstName(user.getFirstName())
        .lastName(user.getLastName())
        .email(user.getEmail())
        .mobileNumber(user.getMobileNumber())
        .roles(roleTemplates)
        .joiningDate(staff.getJoiningDate())
        .department(
            staff.getDepartment() != null ? mapToDepartmentResponse(staff.getDepartment()) : null)
        .designation(
            staff.getDesignation() != null
                ? mapToDesignationResponse(staff.getDesignation())
                : null)
        .user(staff.getUser() != null ? mapToUserResponse(staff.getUser()) : null)
        .build();
  }

  private StaffDto.UserResponse mapToUserResponse(User user) {
    return StaffDto.UserResponse.builder()
        .id(user.getId())
        .userName(user.getUserName())
        .authUserId(user.getAuthUserId())
        .build();
  }

  public StaffDto.DepartmentResponse mapToDepartmentResponse(Department department) {
    return StaffDto.DepartmentResponse.builder()
        .id(department.getId())
        .name(department.getName())
        .description(department.getDescription())
        .active(department.getIs_active())
        .build();
  }

  public StaffDto.DesignationResponse mapToDesignationResponse(Designation designation) {
    return StaffDto.DesignationResponse.builder()
        .id(designation.getId())
        .name(designation.getName())
        .description(designation.getDescription())
        .active(designation.getIs_active())
        .department(
            designation.getDepartment() != null
                ? mapToDepartmentResponse(designation.getDepartment())
                : null)
        .build();
  }
}
