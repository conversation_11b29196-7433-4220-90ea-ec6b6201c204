package com.wexl.retail.lessonplanner.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.DayOfWeek;
import java.util.List;
import lombok.Builder;

public record TeacherTimeTableDto() {

  public record TimeTableRequest(
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("section_uuid") String sectionUuid,
      Long date,
      @JsonProperty("periods_request") List<PeriodsRequest> periodsRequest) {}

  @Builder
  public record PeriodsRequestBulk(
      @JsonProperty("periods_request") List<PeriodsRequest> periodsRequest) {}

  public record PeriodsRequest(
      Long id,
      @JsonProperty("teacher_id") Long teacherId,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("board") String board,
      @JsonProperty("start_time") Long startTime,
      @JsonProperty("end_time") Long endTime,
      @JsonProperty("room_number") String roomNumber,
      @JsonProperty("period_number") Long periodNumber) {}

  @Builder
  public record TimeTableResponse(
      @JsonProperty("teacher_time_table_id") Long teacherTimeTableId,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("dates_response") List<DateResponse> dateResponse) {}

  @Builder
  public record DateResponse(
      Long date,
      @JsonProperty("day_of_week") DayOfWeek dayOfWeek,
      @JsonProperty("teacher_timetable_response") List<PeriodsResponse> teacherTimeTableDetails) {}

  @Builder
  public record PeriodsResponse(
      Long id,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("teacher_subject_id") Long teacherSubjectId,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("teacher_id") Long teacherId,
      @JsonProperty("teacher_name") String teacherName,
      @JsonProperty("start_time") Long startTime,
      @JsonProperty("end_time") Long endTime,
      @JsonProperty("room_number") String roomNumber,
      @JsonProperty("period_number") Long periodNumber,
      @JsonProperty("lesson_planner_id") Long lessonPlannerId) {}

  @Builder
  public record CloneRequest(
      @JsonProperty("weekly_clone") Boolean weeklyClone,
      @JsonProperty("time_table_id") Long timeTableId,
      @JsonProperty("source_from_date") Long sourceFromDate,
      @JsonProperty("source_to_date") Long sourceToDate,
      @JsonProperty("target_from_date") Long targetFromDate,
      @JsonProperty("target_to_date") Long targetToDate) {}
}
