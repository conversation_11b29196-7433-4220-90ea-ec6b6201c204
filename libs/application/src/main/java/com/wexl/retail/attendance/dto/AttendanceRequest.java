package com.wexl.retail.attendance.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AttendanceRequest {
  private String email;
  private String organization;

  @JsonProperty("meeting_id")
  private long meetingId;

  @JsonProperty("first_name")
  private String firstName;

  @JsonProperty("last_name")
  private String lastName;

  @JsonProperty("student_id")
  private long id;

  @JsonProperty("from_time")
  private long meetingStartTime;

  @JsonProperty("to_time")
  private long meetingEndTime;

  @JsonProperty("join_time")
  private long meetingJoinTime;

  @JsonIgnore private long attendanceId;
}
