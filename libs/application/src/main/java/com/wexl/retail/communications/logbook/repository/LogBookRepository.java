package com.wexl.retail.communications.logbook.repository;

import com.wexl.retail.communications.logbook.model.LogBook;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface LogBookRepository extends JpaRepository<LogBook, Long> {

  @Query(
      value =
          """
                            SELECT * FROM log_book WHERE org_slug = :orgSlug
                              AND (CAST((:boardSlug) AS VARCHAR) IS NULL OR board_slug IN (:boardSlug))
                              AND (CAST((:gradeSlug) AS VARCHAR) IS NULL OR grade_slug IN (:gradeSlug))
                              AND (CAST((:sectionUuid) AS VARCHAR) IS NULL OR section_uuid IN (:sectionUuid))
                              AND (CAST((:studentIds) AS VARCHAR) IS NULL OR student_id IN (:studentIds))
                              AND (CAST((:teacherId) AS VARCHAR) IS NULL OR teacher_id IN (:teacherId))
                              order by created_at desc""",
      nativeQuery = true)
  List<LogBook> getLongBookData(
      String orgSlug,
      List<String> boardSlug,
      List<String> gradeSlug,
      List<String> sectionUuid,
      List<Long> studentIds,
      Long teacherId);
}
