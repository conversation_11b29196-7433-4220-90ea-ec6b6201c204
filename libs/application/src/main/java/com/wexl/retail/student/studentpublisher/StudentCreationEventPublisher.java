package com.wexl.retail.student.studentpublisher;

import com.wexl.retail.model.Student;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class StudentCreationEventPublisher {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void studentCreation(final Student student) {
    StudentCreationEvent studentCreationEvent = new StudentCreationEvent(student);
    applicationEventPublisher.publishEvent(studentCreationEvent);
  }
}
