package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StudentDetailReport extends AbstractMetricHandler implements MetricHandler {

  private final ReportCardService reportCardService;

  @Override
  public String name() {
    return "student-detail-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    String authUserId = genericMetricRequest.getInput().get(AUTHUSERID).toString();
    List<String> fromDate =
        Optional.ofNullable(genericMetricRequest.getInput().get(FROM_DATE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    return reportCardService.detailsByStudent(authUserId, fromDate.getFirst());
  }
}
