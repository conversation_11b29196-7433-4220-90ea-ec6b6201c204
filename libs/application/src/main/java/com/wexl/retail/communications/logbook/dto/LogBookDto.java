package com.wexl.retail.communications.logbook.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record LogBookDto() {
  public record Request(
      String title,
      String description,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("student_ids") List<Long> studentIds,
      @JsonProperty("section_uuids") String sectionUuids,
      List<String> attachment,
      LogBookType type,
      List<String> link) {}

  @Builder
  public record Response(
      Long id,
      String title,
      String description,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("teacher_id") Long teacherId,
      @JsonProperty("teacherName") String teacherName,
      @JsonProperty("created_at") Long createdAt,
      List<String> attachment,
      LogBookType type) {}
}
