package com.wexl.pallavi.preprimary.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "holistic_report_data_competency")
public class HolisticReportCompetency {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private String studentId;

  private String subject;

  private String skill;
  private String parameter;
  private String grade;
}
