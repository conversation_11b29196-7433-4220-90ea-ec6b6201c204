package com.wexl.dps.managereportcard.repository;

import com.wexl.dps.dto.InterReportCardData;
import com.wexl.dps.managereportcard.model.ReportCardConfigData;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.UpperGradeReportCardData;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportCardConfigDataRepository extends JpaRepository<ReportCardConfigData, Long> {
  List<ReportCardConfigData> findAllByOrgSlugAndReportCardConfigDetailIn(
      String orgSlug, List<ReportCardConfigDetail> reportCardConfigDetail);

  @Query(
      value =
          """
          select DISTINCT sm."name" AS subjectName ,otd.title as testName, sm.wexl_subject_slug as subjectSlug ,COALESCE(rcc.calculated_marks,0) AS marks, rccd.weightage as totalMarks,
                 sm.seq_no as seqNo,sm."type" as type,sm.category, ta.slug as assessmentSlug , otss.is_attended as isAttended,rcc.actual_marks as actualMarks,
                 otss.remarks as remarks,ots.marks as subjectMarks, otd.exam_start_date as startDate,ots.offline_test_definition_id as otdId, ots.consider_percentage as considerPercentage
                  from report_card_config_data rcc
                  join report_card_config_details rccd on  rcc.report_card_config_detail_id  = rccd.id
                  join offline_test_schedule_student otss ON otss.id = rcc.otss_id
                  join offline_test_schedule ots on ots.id  = otss.offline_test_schedule_id
                  join offline_test_definition otd on otd.id =ots.offline_test_definition_id
                  join term_assessments ta on ta.id = rccd.term_assessment_id
                  join subject_metadata_students sms on sms.student_id = rcc.student_id
                  join subject_metadata sm on sm.id = sms.subject_metadata_id and ots.subject_metadata_id  = sm.id
                    where ots.deleted_at is  null and rcc.student_id = :studentId and rccd.term_assessment_id in (:assessmentIds)""",
      nativeQuery = true)
  List<LowerGradeReportCardData> getStudentReportByStudentAndAssessments(
      Long studentId, List<Long> assessmentIds);

  @Query(
      value =
          """
                          select DISTINCT sm."name" AS subjectName ,COALESCE(rcc.calculated_marks,0) AS marks, rccd.weightage as totalMarks,
                            sm.seq_no as seqNo,sm."type" as type,sm.category, ta.slug as assessmentSlug ,tac."name" as assessmentCategory, otss.is_attended as isAttended, otss.remarks as remarks
                            from report_card_config_data rcc
                            join report_card_config_details rccd on  rcc.report_card_config_detail_id  = rccd.id
                            join offline_test_schedule_student otss ON otss.id = rcc.otss_id
                            join offline_test_schedule ots on ots.id  = otss.offline_test_schedule_id
                            join offline_test_definition otd on ots.offline_test_definition_id = otd.id
                            join term_assessments ta on ta.id = rccd.term_assessment_id
                            join term_assessment_categories tac on otd.assessment_category_id  = tac.id\s
                            join subject_metadata_students sms on sms.student_id = rcc.student_id
                            join subject_metadata sm on sm.id = sms.subject_metadata_id and ots.subject_metadata_id  = sm.id
                            where rcc.student_id = :studentId and ots.offline_test_definition_id in (:otdIds)""",
      nativeQuery = true)
  List<LowerGradeReportCardData> getStudentReportByStudentAndOfflineTestDefinitions(
      Long studentId, List<Long> otdIds);

  Optional<ReportCardConfigData> findByOfflineTestScheduleStudentId(Long otssId);

  @Query(
      value =
          """
                          SELECT sm."name" AS subjectName,COALESCE(otss.marks, 0) AS marks,
                          ots.marks AS totalMarks,COALESCE(highest_marks.highestMarks,0) as highestMarks,otd.title,otss.is_attended AS isAttended,
                          sm.seq_no AS seqNo,sm."type" AS type,sm.category,term_slug AS termSlug,otd.assessment_slug AS assessmentSlug
                         FROM offline_test_definition otd JOIN offline_test_schedule ots ON otd.id = ots.offline_test_definition_id
                         JOIN offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                         JOIN (SELECT offline_test_schedule_id,MAX(marks) AS highestMarks FROM offline_test_schedule_student
                         GROUP BY offline_test_schedule_id) AS highest_marks ON highest_marks.offline_test_schedule_id = ots.id
                         JOIN subject_metadata_students sms ON sms.student_id = otss.student_id
                         JOIN subject_metadata sm ON sm.id = sms.subject_metadata_id AND ots.subject_name = sm."name"
                         WHERE otd.org_slug =:orgSlug AND otss.student_id =:studentId
                         GROUP BY sm."name", otss.marks, ots.marks, highest_marks.highestMarks, otd.title, otss.is_attended, sm.seq_no,
                         sm."type", sm.category, term_slug, otd.assessment_slug
                          ORDER BY sm.seq_no ASC;
                                                  """,
      nativeQuery = true)
  List<InterReportCardData> getInterReportCard(Long studentId, String orgSlug);

  @Query(
      value =
          """
          select distinct  sm."name" AS subjectName ,COALESCE(rcc.calculated_marks,0) AS marks,
                   case when  rccd.weightage is null then ots.marks  else rccd.weightage end  as totalMarks,
                   case when rccd.weightage is null then COALESCE(hm.highestMarks,0) else
                    COALESCE(round((hm.highestMarks/ots.marks)*rccd.weightage,2),0) end
                    as highestMarks,sm.wexl_subject_slug as wexlSubject,
                          sm.seq_no as seqNo,sm."type" as type,sm.category, ta.slug as assessmentSlug , otss.is_attended as isAttended, otss.remarks as remarks
                           from report_card_config_data rcc
                           join report_card_config_details rccd on  rcc.report_card_config_detail_id  = rccd.id
                           join offline_test_schedule_student otss ON otss.id = rcc.otss_id
                           join offline_test_schedule ots on ots.id  = otss.offline_test_schedule_id
                            JOIN (SELECT offline_test_schedule_id,MAX(marks) AS highestMarks FROM offline_test_schedule_student
                          GROUP BY offline_test_schedule_id) AS hm ON hm.offline_test_schedule_id = ots.id
                           join term_assessments ta on ta.id = rccd.term_assessment_id
                           join subject_metadata_students sms on sms.student_id = rcc.student_id
                           join subject_metadata sm on sm.id = sms.subject_metadata_id and ots.subject_metadata_id  = sm.id
                             where rcc.student_id =:studentId and rccd.term_assessment_id in (:assessments) and ots.deleted_at is null""",
      nativeQuery = true)
  List<InterReportCardData> getStudentReportByAssessments(Long studentId, List<Long> assessments);

  @Query(
      value =
          """
                  select DISTINCT sm."name" AS subjectName ,COALESCE(rcc.calculated_marks,0) AS marks, rccd.weightage as totalMarks, ots.marks as subjectMarks,
                         otd.title as title, sm.seq_no as seqNo,sm."type" as type,sm.category, ta.slug as assessmentSlug , otss.is_attended as isAttended, otss.remarks as remarks, otss.marks as actualMarks
                          from report_card_config_data rcc
                          join report_card_config_details rccd on  rcc.report_card_config_detail_id  = rccd.id
                          join offline_test_schedule_student otss ON otss.id = rcc.otss_id
                          join offline_test_schedule ots on ots.id  = otss.offline_test_schedule_id
                          join offline_test_definition otd on ots.offline_test_definition_id = otd.id
                          join term_assessments ta on ta.id = rccd.term_assessment_id
                          join subject_metadata_students sms on sms.student_id = rcc.student_id
                          join subject_metadata sm on sm.id = sms.subject_metadata_id and ots.subject_metadata_id  = sm.id
                            where rcc.student_id = :studentId and rccd.term_assessment_id in (:assessmentIds) and ots.deleted_at is null""",
      nativeQuery = true)
  List<UpperGradeReportCardData> getUpperGradeReportByStudentAndAssessments(
      Long studentId, List<Long> assessmentIds);

  boolean existsByReportCardConfigDetail(ReportCardConfigDetail reportCardConfigDetail);

  @Query(
      value =
          """
          select  sm."name" as subjectName ,rccd.term_assessment_name , max(rccd.calculated_marks) as highestMarks,  sm.seq_no as seqNo
          from report_card_config_data rccd
          join students st on st.id = rccd.student_id
          join sections sc on sc.id = st.section_id
          join offline_test_schedule_student otss on otss.student_id = rccd.student_id  and rccd.otss_id = otss.id
          join offline_test_schedule ots on ots.id = otss.offline_test_schedule_id
          join offline_test_definition otd  on otd.id = ots.offline_test_definition_id
          join subject_metadata sm on sm.id = ots.subject_metadata_id
          where sc.organization = :organization and sc.grade_slug = :gradeSlug and ots.deleted_at  is null  and sm.deleted_at is null
          and (cast((:categories) as varChar) is null or otd.assessment_category_id in (:categories))
          group by sm."name",rccd.term_assessment_name, sm.seq_no""",
      nativeQuery = true)
  List<InterReportCardData> getHighestMarksReportData(
      String gradeSlug, String organization, Set<Long> categories);

  @Query(
      value =
          """
          SELECT
              rcc.student_id as studentId,
              round((SUM(rcc.calculated_marks) / SUM(
                  CASE
                      WHEN rccd.weightage IS NULL THEN ots.marks
                      ELSE rccd.weightage
                  END
              )) * 100) AS overallPercentage
          FROM report_card_config_data rcc
          JOIN report_card_config_details rccd ON rcc.report_card_config_detail_id = rccd.id
          JOIN offline_test_schedule_student otss ON otss.id = rcc.otss_id
          JOIN  offline_test_schedule ots ON ots.id = otss.offline_test_schedule_id
          JOIN (SELECT  offline_test_schedule_id,
                  MAX(marks) AS highestMarks
               FROM  offline_test_schedule_student
               GROUP BY  offline_test_schedule_id) AS hm ON hm.offline_test_schedule_id = ots.id
          JOIN  term_assessments ta ON ta.id = rccd.term_assessment_id
          JOIN  subject_metadata_students sms ON sms.student_id = rcc.student_id
          JOIN  subject_metadata sm ON sm.id = sms.subject_metadata_id
              AND ots.subject_metadata_id = sm.id
          WHERE
              rcc.student_id IN (:studentIds) and sm.category = 'SCHOLASTIC' and sm."type" = 'MANDATORY'
              AND rccd.term_assessment_id IN (:assessmentIds)
              AND ots.deleted_at IS NULL
          GROUP BY  rcc.student_id order by overallPercentage  desc""",
      nativeQuery = true)
  List<InterReportCardData> getClassWiseTopperMarks(
      List<Long> studentIds, List<Long> assessmentIds);
}
