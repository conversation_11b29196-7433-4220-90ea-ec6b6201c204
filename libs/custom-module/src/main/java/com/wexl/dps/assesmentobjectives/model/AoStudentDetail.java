package com.wexl.dps.assesmentobjectives.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "ao_student_details")
public class AoStudentDetail extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "ao_student_id")
  private AoStudent aoStudent;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "assessment_Objective_detail_id")
  private AssessmentObjectiveDetail assessmentObjectiveDetail;

  private String color;
}
