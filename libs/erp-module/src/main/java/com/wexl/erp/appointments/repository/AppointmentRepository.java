package com.wexl.erp.appointments.repository;

import com.wexl.erp.appointments.model.Appointment;
import com.wexl.erp.appointments.model.AppointmentStatus;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.model.Student;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {

  List<Appointment> findByOrgSlugAndGuardianOrderByAppliedDateDescStatusAsc(
      String orgSlug, Guardian guardian);

  List<Appointment> findByStatus(AppointmentStatus status);

  @Query(
      value =
          "select a.* from students s join appointments a on a.student_id = s.id where s.section_id in :sectionIds order by a.applied_date desc, CASE WHEN a.status = 'PENDING' THEN 0 ELSE 1 END",
      nativeQuery = true)
  List<Appointment> findBySectionIds(List<Long> sectionIds);

  List<Appointment> findAllByOrderByAppliedDateDescStatusAsc();

  List<Appointment> findByStudent(Student student);

  List<Appointment> findByOrgSlugAndGuardianAndRoleOrderByAppliedDateDescStatusAsc(
      String orgSlug, Guardian guardian, String role);
}
