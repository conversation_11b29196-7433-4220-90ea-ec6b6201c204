package com.wexl.erp.fees.controller;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.service.FeeHeadService;
import com.wexl.erp.fees.service.FeeService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class FeeController {
  private final FeeService feeService;
  private final FeeHeadService feeHeadService;

  @PostMapping("/fee-types")
  public void saveFeeType(
      @PathVariable String orgSlug, @RequestBody FeeDto.FeeTypeRequest request) {
    feeService.saveFeeTypes(orgSlug, request);
  }

  @GetMapping("/fee-types")
  public List<FeeDto.FeeTypeResponse> getFeeTypes(@PathVariable String orgSlug) {
    return feeService.getFeeTypes(orgSlug);
  }

  @PutMapping("/fee-types/{feeTypeId}")
  public void updateFeeTypeById(
      @PathVariable String orgSlug,
      @PathVariable String feeTypeId,
      @RequestBody FeeDto.FeeTypeRequest request) {
    feeService.updateFeeTypeById(orgSlug, feeTypeId, request);
  }

  @DeleteMapping("/fee-types/{feeTypeId}")
  public void deleteFeeTypeById(@PathVariable String orgSlug, @PathVariable String feeTypeId) {
    feeService.deleteFeeTypeById(orgSlug, feeTypeId);
  }

  @PostMapping("/fee-groups")
  public void saveFeeGroup(
      @PathVariable String orgSlug, @RequestBody FeeDto.FeeGroupRequest request) {
    feeService.saveFeeGroup(orgSlug, request);
  }

  @GetMapping("/fee-groups")
  public List<FeeDto.FeeGroupResponse> getFeeGroup(@PathVariable String orgSlug) {
    return feeService.getFeeGroup(orgSlug);
  }

  @PutMapping("/fee-groups/{feeGroupId}")
  public void updateFeeGroupById(
      @PathVariable String orgSlug,
      @PathVariable String feeGroupId,
      @RequestBody FeeDto.FeeGroupRequest request) {
    feeService.updateFeeGroupById(orgSlug, feeGroupId, request);
  }

  @DeleteMapping("/fee-groups/{feeGroupId}")
  public void deleteFeeGroupById(@PathVariable String orgSlug, @PathVariable String feeGroupId) {
    feeService.deleteFeeGroupById(orgSlug, feeGroupId);
  }

  @PostMapping("/fee-groups/{feeGroupId}/publish")
  public void publishFeeGroup(@PathVariable String orgSlug, @PathVariable String feeGroupId) {
    feeService.publishFeeGroup(orgSlug, feeGroupId);
  }

  @PostMapping("/fee-groups/{feeGroupId}/fee-types")
  public void saveFeeGroupFeeTypes(
      @PathVariable String feeGroupId,
      @PathVariable String orgSlug,
      @RequestBody FeeDto.FeeGroupFeeTypeRequest request) {
    feeService.saveFeeGroupFeeTypes(feeGroupId, orgSlug, request);
  }

  @GetMapping("/fee-groups/{feeGroupId}/fee-types")
  public List<FeeDto.FeeGroupFeeTypeResponse> getFeeGroupFeeTypes(
      @PathVariable String feeGroupId, @PathVariable String orgSlug) {
    return feeService.getFeeGroupFeeTypes(feeGroupId, orgSlug);
  }

  @DeleteMapping("/fee-groups/{feeGroupId}/fee-types/{feeTypeId}")
  public void deleteFeeGroupFeeTypeById(
      @PathVariable String feeGroupId,
      @PathVariable String orgSlug,
      @PathVariable String feeTypeId) {
    feeService.deleteFeeGroupFeeTypeById(orgSlug, feeTypeId, feeGroupId);
  }

  @PutMapping("/fee-groups/{feeGroupId}/fee-types/{feeTypeId}")
  public void updateFeeGroupFeeTypeById(
      @PathVariable String orgSlug,
      @PathVariable String feeGroupId,
      @RequestBody FeeDto.FeeGroupFeeTypeUpdateRequest request,
      @PathVariable String feeTypeId) {
    feeService.updateFeeGroupFeeTypeById(orgSlug, feeGroupId, feeTypeId, request);
  }

  @PostMapping("/fee-masters")
  public void saveFeeMaster(
      @RequestBody FeeDto.FeeMasterRequest request, @PathVariable String orgSlug) {
    feeService.saveFeeMaster(orgSlug, request);
  }

  @GetMapping("/fee-masters")
  public List<FeeDto.FeeMasterResponse> getFeeMaster(@PathVariable String orgSlug) {
    return feeService.getFeeMaster(orgSlug);
  }

  @GetMapping("/fee-masters/{feeMasterId}")
  public List<FeeDto.FeeHeadResponse> getFeeHeadsByFeeMaster(
      @PathVariable String orgSlug, @PathVariable String feeMasterId) {
    return feeService.getFeeHeadsByFeeMaster(orgSlug, feeMasterId);
  }

  @GetMapping("/students/{studentId}/fee-heads")
  public List<FeeDto.StudentsFeeHeadResponse> getFeeHeadsByStudent(
      @PathVariable String orgSlug, @PathVariable String studentId) {
    return feeHeadService.getFeeHeadsByStudent(orgSlug, studentId);
  }
}
