package com.wexl.erp.infirmary.repository;

import com.wexl.erp.infirmary.model.InfirmaryEntry;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface InfirmaryRepository extends JpaRepository<InfirmaryEntry, Long> {
  List<InfirmaryEntry> findAllByStudentIdOrderByIdDesc(Long studentId);

  List<InfirmaryEntry> findAllByOrgSlug(String orgSlug);

  @Query(
      value =
          """
                  SELECT ie.*
                  FROM infirmary_entries ie
                  JOIN students s ON ie.student_id = s.id
                  JOIN sections sec ON s.section_id = sec.id
                  WHERE sec.grade_slug =:gradeSlug
                  AND CAST(sec.uuid AS varchar) = :sectionUuid
                   AND sec.organization = :orgSlug
                   ORDER BY ie.created_at DESC
          """,
      nativeQuery = true)
  List<InfirmaryEntry> findAllByTeacherGradeAndSection(
      String orgSlug, String gradeSlug, String sectionUuid);

  List<InfirmaryEntry> findByStudentIdAndOrgSlug(Long studentId, String orgSlug);
}
