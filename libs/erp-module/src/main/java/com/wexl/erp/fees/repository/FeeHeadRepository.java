package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FeeMaster;
import com.wexl.erp.fees.model.FeeType;
import com.wexl.retail.model.Student;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeHeadRepository extends JpaRepository<FeeHead, Long> {

  boolean existsByFeeMasterAndStudentAndFeeType(
      FeeMaster feeMaster, Student student, FeeType feeType);

  List<FeeHead> findAllByFeeMasterAndOrgSlug(FeeMaster feeMaster, String orgSlug);

  List<FeeHead> findAllByStudentIdAndOrgSlug(Long studentId, String orgSlug);
}
