<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xi="http://www.w3.org/2001/XInclude" >
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body  margin="12mm"  />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:table table-layout="fixed" width="100%" border="1px solid black">
                    <fo:table-column column-width="6%"/>
                    <fo:table-column column-width="94%"/>
                    <fo:table-body>
                        <th:block th:each="i : ${#numbers.sequence(1, 30)}">
                            <fo:table-row height="8mm">
                                <fo:table-cell border="1px solid black" >
                                    <fo:block padding-top="2mm" text-align="center" th:if="${i >= 2 and i <= 11}" th:text="${i - 1}"></fo:block>
                                    <fo:block th:if="${i < 2 or i > 11}">&#160;</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1px solid black">
                                    <fo:block th:if="${i == 1}"   font-weight="bold">
                                        <fo:block padding-top="7mm" margin-left="2mm">
                                            <fo:inline>Student name:</fo:inline>
                                            <fo:inline th:text="${model.body.name}"></fo:inline>
                                        </fo:block>
                                        <fo:block  padding-top="-4.8mm" margin-left="67%">
                                            <fo:inline>Roll number:</fo:inline>
                                            <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                                        </fo:block>
                                    </fo:block>
                                    <fo:block th:unless="${i == 1}">&#160;</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </th:block>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>

        </fo:flow>
    </fo:page-sequence>
</fo:root>