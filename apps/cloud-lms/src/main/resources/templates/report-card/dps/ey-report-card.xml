<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice" page-height="210mm" page-width="297mm">
            <fo:region-body margin="12mm" />
            <fo:region-before margin="10mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="93%" margin-top="0mm"  border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="20%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter2">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7.5" />
                                            <feFuncG type="linear" slope="7.5"/>
                                            <feFuncB type="linear" slope="7.5"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug == 'pal454783'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>

                                <image filter="url(#brightnessFilter2)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != 'pal454783'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="40mm" />
                    <fo:table-column column-width="190mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="22pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                           th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt" th:if="${model.body.orgSlug} != 'del189476'">
                                    CAMBRIDGE INTERNATIONAL EDUCATION
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                    LEARNING MILESTONES REPORT - 2024-25
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic th:src="${ model.body.orgSlug == 'del189476' ? ' ' : 'url(https://images.wexledu.com/logo3.jpeg)'}"
                                                         content-width="45mm" content-height="non-uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" space-before="15pt" space-after="16pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="34mm" />
                        <fo:table-column column-width="70mm" />
                        <fo:table-column column-width="38mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row space-after="5pt">
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm">CANDIDATE NAME&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm" th:text="${#strings.toUpperCase(model.body.name)}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm">GRADE FACILITATOR&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm" th:text="${#strings.toUpperCase(model.body.gradeFacilitatorName)}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm">GRADE&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm" th:text="${model.body.grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row space-after="5pt">
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm">SCHOOL SECTION&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block margin-bottom="3mm" th:text="${model.body.schoolSectionName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block>ASSESSMENT&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block th:text="${#strings.toUpperCase(model.body.assessment)}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block>ATTENDANCE&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block th:text="${model.body.attendance}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block>CENTRE NO &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block th:if="${model.body.orgSlug == 'pal454783'}">
                                        <fo:block>
                                            IA697
                                        </fo:block>
                                    </fo:block>
                                    <fo:block th:if="${model.body.orgSlug == 'del909850'}">
                                        <fo:block>
                                            IA700
                                        </fo:block>
                                    </fo:block>
                                    <fo:block th:if="${model.body.orgSlug == 'del765517'}">
                                        <fo:block>
                                            IA699
                                        </fo:block>
                                    </fo:block>
                                    <fo:block th:if="${model.body.orgSlug != 'pal454783' and model.body.orgSlug != 'del909850' and model.body.orgSlug != 'del765517'}">
                                        <fo:block>
                                            IN174
                                        </fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center"  space-after="15pt">
                    <fo:block   font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.firstTable.title}"></fo:block>
                    <fo:table >
                        <fo:table-column  column-width="65mm"  />
                        <fo:table-column column-width="53mm" />
                        <fo:table-column column-width="18mm"  />
                        <fo:table-column column-width="65mm"  />
                        <fo:table-column column-width="53mm"  />
                        <fo:table-column column-width="18mm"  />
                        <fo:table-header font-size="10pt" >
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>AREAS OF LEARNING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>LEARNING LEVEL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>AREAS OF LEARNING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>LEARNING LEVEL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block font-weight="bold">COMMUNICATION, LANGUAGE &amp; LITERACY - LISTENING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.listeningValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.listeningGrade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" th:if="${model.body.orgSlug} != 'del189476'">
                                    <fo:block font-weight="bold">MATHEMATICS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" th:if="${model.body.orgSlug} == 'del189476'">
                                    <fo:block font-weight="bold">NUMERACY</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.mathValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.mathGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block font-weight="bold">COMMUNICATION, LANGUAGE &amp; LITERACY - SPEAKING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.speakingValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.speakingGrade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block font-weight="bold">UNDERSTANDING OF THE WORLD</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.untwValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.untwGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block font-weight="bold">COMMUNICATION, LANGUAGE &amp; LITERACY - READING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.readingValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.readingGrade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block font-weight="bold">PERSONAL,SOCIAL AND EMOTIONAL DEVELOPMENT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.psewValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.psewGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block font-weight="bold">COMMUNICATION, LANGUAGE &amp; LITERACY - WRITING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.writingValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.writingGrade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" th:if="${model.body.orgSlug} != 'del189476'">
                                    <fo:block font-weight="bold">PHYSICAL DEVELOPMENT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" th:if="${model.body.orgSlug} == 'del189476'">
                                    <fo:block font-weight="bold">PHYSICAL DEVELOPMENT / CREATIVE EXPRESSIONS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.phyDevValue}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.phyDevGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:if="${model.body.orgSlug} == 'del189476' and ${model.body.gradeSlug} != 'nur'">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block font-weight="bold">TELUGU / HINDI</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.teluguOrHindiValue}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-top="2mm">
                                    <fo:block th:text="${model.body.firstTable.teluguOrHindiGrade}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="11pt"   space-before="8mm" th:if="${model.body.orgSlug} != 'del189476'">
                    <fo:block space-after="3mm" font-size="12" font-weight="bold" text-align="left" font-family="Times New Roman, serif">GRADE FACILITATOR'S COMMENTS:</fo:block>
                    <fo:table border="1pt solid black"  >
                        <fo:table-column  column-width="100%"  />
                        <fo:table-body  font-family="Times New Roman, serif">
                            <fo:table-row  >
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.comment}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del189476'">
                    <fo:block border-width="1mm" font-size="11pt"   space-before="8mm">
                        <fo:block space-after="3mm" font-size="12" font-weight="bold" text-align="left" font-family="Times New Roman, serif">TEACHER'S COMMENTS:</fo:block>
                        <fo:table border="1pt solid black"  >
                            <fo:table-column  column-width="100%"  />
                            <fo:table-body  font-family="Times New Roman, serif">
                                <fo:table-row  >
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${model.body.firstTable.comment}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                </fo:block>
                <!-- Signature Block-->
                <fo:block th:if="${model.body.orgSlug} != 'dps688668' and  ${model.body.orgSlug} != 'del765517' and  ${model.body.orgSlug} != 'del909850' and
                  ${model.body.orgSlug} != 'pal454783' and ${model.body.orgSlug} != 'del189476'">
                    <fo:block border-width="1mm" font-size="10pt" space-before="60pt" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="410mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>HEADMISTRESS</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>PRINCIPAL</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del189476'">
                    <fo:block border-width="1mm" font-size="10pt" space-before="60pt" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="70mm" />
                            <fo:table-column column-width="130mm" />
                            <fo:table-column column-width="100mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>CLASS TEACHER</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>HEADMISTRESS</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>PRINCIPAL</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'dps688668'">
                    <fo:block th:replace="report-card/dps/eySignature.xml :: ${model.body.termSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del765517'">
                    <fo:block th:replace="report-card/dps/eySignature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del909850'">
                    <fo:block th:replace="report-card/dps/eySignature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'pal454783'">
                    <fo:block th:replace="report-card/dps/eySignature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--second page-->
    <fo:page-sequence master-reference="invoice">
        <fo:static-content flow-name="xsl-region-before" th:if="${model.body.orgSlug == 'del189476' }">
            <fo:block-container absolute-position="absolute" top="155mm" left="12mm">
                <fo:block>
                    <fo:block font-weight="bold" padding="1mm" padding-left="3mm" text-align="left" font-size="13pt"
                              font-family="Times New Roman, serif">
                        Learning Statements Learning Level Indicator  :
                    </fo:block>
                    <fo:table table-layout="fixed" width="100%" border="none" font-family="Times New Roman, serif" >
                        <fo:table-column column-width="10mm"/>
                        <fo:table-column column-width="250mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="none">
                                    <fo:block>
                                        <fo:instream-foreign-object width="20pt" height="20pt">
                                            <svg width="30%" height="30%" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="25%" cy="25%" r="25%" fill="red"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="1.5mm">
                                    <fo:block font-size="11pt" padding-left="5pt">Red - Evolving to Progressing</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="none">
                                    <fo:block space-before="10pt">
                                        <fo:instream-foreign-object width="20pt" height="20pt">
                                            <svg width="30%" height="30%" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="25%" cy="25%" r="25%" fill="orange"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="11pt" padding-left="5pt" padding-top="1.5mm">Amber - Progressing to Prominent</fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell border="none">
                                    <fo:block space-before="10pt">
                                        <fo:instream-foreign-object width="20pt" height="20pt">
                                            <svg width="30%" height="30%" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="25%" cy="25%" r="25%" fill="green"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="11pt" padding-left="5pt" padding-top="1.5mm">Green - Confident to Skillfull</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:static-content>
        <fo:flow flow-name="xsl-region-body">
            <!-- Content of the second page -->
            <fo:block-container width="100%" height="93%" margin-top="0mm"  border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="20%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter2">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7.5"/>
                                            <feFuncG type="linear" slope="7.5"/>
                                            <feFuncB type="linear" slope="7.5"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug == 'pal454783'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>

                                <image filter="url(#brightnessFilter2)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != 'pal454783'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="40mm" />
                    <fo:table-column column-width="190mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="22pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt" th:if="${model.body.orgSlug} != 'del189476'">
                                    CAMBRIDGE INTERNATIONAL EDUCATION
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                    LEARNING MILESTONES REPORT - 2024-25
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic th:src="${ model.body.orgSlug == 'del189476'? ' ' : 'url(https://images.wexledu.com/logo3.jpeg)'}"
                                                         content-width="45mm" content-height="non-uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" text-align="center" space-before="5mm"  space-after="15pt">
                    <fo:block   font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondPage.firstTable.firstTableTitle}"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column  column-width="65mm"  />
                        <fo:table-column column-width="52mm" />
                        <fo:table-column column-width="52mm"  />
                        <fo:table-column column-width="52mm"  />
                        <fo:table-column column-width="52mm"  />

                        <fo:table-header font-size="10pt" >
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>AREAS OF LEARNING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableAreas.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableAreas.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableAreas.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableAreas.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black"  padding="1mm"  font-weight="bold" text-align="left">
                                    <fo:block>LEARNING LEVEL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableLearningLevel.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableLearningLevel.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableLearningLevel.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableLearningLevel.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black"  padding="1mm"  font-weight="bold" text-align="left">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableGrade.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableGrade.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableGrade.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.firstTable.firstTableGrade.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:if="${model.body.secondPage.secondTable.secondTableTitle != null}"
                          border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" text-align="center" space-after="15pt">
                    <fo:block font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondPage.secondTable.secondTableTitle}"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="62mm" />
                        <fo:table-column column-width="42mm" />
                        <fo:table-column column-width="42mm" />
                        <fo:table-column column-width="42mm" />
                        <fo:table-column column-width="42mm" />
                        <th:block th:if="${model.body.secondPage.secondTable.secondTableAreas.column5 != null}">
                            <fo:table-column column-width="42mm"/>
                        </th:block>
                        <fo:table-header font-size="10pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>AREAS OF LEARNING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableAreas.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableAreas.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableAreas.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableAreas.column4}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.secondPage.secondTable.secondTableAreas.column5 != null}">
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondPage.secondTable.secondTableAreas.column5}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="left">
                                    <fo:block>LEARNING LEVEL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableLearningLevel.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableLearningLevel.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableLearningLevel.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableLearningLevel.column4}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.secondPage.secondTable.secondTableLearningLevel.column5 != null}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondPage.secondTable.secondTableLearningLevel.column5}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                            </fo:table-row>
                            <fo:table-row th:if="${model.body.secondPage.secondTable.secondTableGrade.column1 != null}">
                                <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="left">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableGrade.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableGrade.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableGrade.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.secondTable.secondTableGrade.column4}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.secondPage.secondTable.secondTableGrade.column5 != null}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondPage.secondTable.secondTableGrade.column5}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:if="${model.body.secondPage.thirdTable.thirdTableTitle != null}"
                        border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" text-align="center"  space-after="15pt">
                    <fo:block   font-size="12" font-weight="bold" font-family="Times New Roman, serif" space-after="5pt" th:text="${model.body.secondPage.thirdTable.thirdTableTitle}"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column  column-width="48mm"  />
                        <fo:table-column column-width="45mm" />
                        <fo:table-column column-width="45mm"  />
                        <fo:table-column column-width="45mm"  />
                        <fo:table-column column-width="45mm"  />
                        <th:block th:if="${model.body.secondPage.thirdTable.thirdTableAreas.column5 != null}">
                            <fo:table-column column-width="45mm"/>
                        </th:block>

                        <fo:table-header font-size="10pt" >
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>AREAS OF LEARNING</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableAreas.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableAreas.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableAreas.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableAreas.column4}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.secondPage.thirdTable.thirdTableAreas.column5 != null}">
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableAreas.column5}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                            </fo:table-row>

                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black"  padding="1mm"  font-weight="bold" text-align="left">
                                    <fo:block>LEARNING LEVEL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableLearningLevel.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableLearningLevel.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableLearningLevel.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableLearningLevel.column4}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.secondPage.thirdTable.thirdTableLearningLevel.column5 != null}">
                                    <fo:table-cell border="1pt solid black"  padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableLearningLevel.column5}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                            </fo:table-row>
                            <fo:table-row th:if="${model.body.secondPage.thirdTable.thirdTableGrade.column1 != null}">
                                <fo:table-cell border="1pt solid black" padding="1mm" font-weight="bold" text-align="left">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableGrade.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableGrade.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableGrade.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableGrade.column4}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.secondPage.thirdTable.thirdTableGrade.column5 != null}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                        <fo:block th:text="${model.body.secondPage.thirdTable.thirdTableGrade.column5}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--third page-->
    <fo:page-sequence master-reference="invoice" th:if="${model.body.orgSlug != 'del189476'}">
        <fo:flow flow-name="xsl-region-body">
            <!-- Content of the second page -->
            <fo:block-container width="100%" height="93%" margin-top="0mm"  border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="20%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter2">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7.5"/>
                                            <feFuncG type="linear" slope="7.5"/>
                                            <feFuncB type="linear" slope="7.5"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug == 'pal454783'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>

                                <image filter="url(#brightnessFilter2)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != 'pal454783'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="40mm" />
                    <fo:table-column column-width="190mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="22pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt" th:if="${model.body.orgSlug} != 'del189476'">
                                    CAMBRIDGE INTERNATIONAL EDUCATION
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="13pt">
                                    LEARNING MILESTONES REPORT - 2024-25
                                </fo:block>
                                <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt">
                                    ACADEMIC PERFORMANCE GRADING RUBRIC
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic th:src="${ model.body.orgSlug == 'del189476'? ' ' : 'url(https://images.wexledu.com/logo3.jpeg)'}"
                                                         content-width="45mm" content-height="non-uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt"   space-before="14mm" text-align="left"></fo:block>
                <fo:block  font-size="14" font-weight="bold" text-align="left" font-family="Times New Roman, serif" space-after="1mm">CAMBRIDGE AREAS OF LEARNING </fo:block>
                <fo:block  font-size="14" font-weight="bold" text-align="left" font-family="Times New Roman, serif" space-after="1mm">Learner Learning Level &amp; Grading Rubric : </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Skillful Learner - A* </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Confident Learner - A </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Prominent Learner - B </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Progressing Learner - C </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Evolving Learner - D </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Absent - ABS </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Medical Leave -ML </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Permitted Absent -PA </fo:block>
                <fo:block  font-size="14" font-weight="bold" text-align="left" font-family="Times New Roman, serif" space-after="1mm">CAMBRIDGE LEARNER ATTRIBUTES </fo:block>
                <fo:block  font-size="14" font-weight="bold" text-align="left" font-family="Times New Roman, serif" space-after="1mm">Learner - Grading Rubric </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Always </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Often </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Occasionally </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Rarely </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Never </fo:block>
                <fo:block  font-size="13"  text-align="left" font-family="Times New Roman, serif" space-after="1mm">Absent </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fourth table-->
    <fo:page-sequence master-reference="invoice"
                      background-position-horizontal="bottom"
                      background-position-vertical="bottom">
        <fo:static-content flow-name="xsl-region-before">
            <fo:block-container width="100%" height="93%" margin-top="12mm" border="2pt solid black" padding="6mm"
                         padding-left="-6mm" padding-bottom="184mm" padding-right="-6mm">
            <fo:block-container absolute-position="absolute" top="50mm" left="50%" width="0%" height="0%">
                <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                    <fo:instream-foreign-object content-width="300%" content-height="300%">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                            <defs>
                                <filter id="brightnessFilter1">
                                    <feComponentTransfer>
                                        <feFuncR type="linear" slope="7"/>
                                        <feFuncG type="linear" slope="7"/>
                                        <feFuncB type="linear" slope="7"/>
                                    </feComponentTransfer>
                                </filter>
                                <filter id="brightnessFilter2">
                                    <feComponentTransfer>
                                        <feFuncR type="linear" slope="7.5"/>
                                        <feFuncG type="linear" slope="7.5"/>
                                        <feFuncB type="linear" slope="7.5"/>
                                    </feComponentTransfer>
                                </filter>
                            </defs>
                            <image filter="url(#brightnessFilter1)"
                                   x="0" y="0"
                                   width="100%" height="100%"
                                   th:if="${model.body.orgSlug == 'pal454783'}"
                                   xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>

                            <image filter="url(#brightnessFilter2)"
                                   x="0" y="0"
                                   width="100%" height="100%"
                                   th:if="${model.body.orgSlug != 'pal454783'}"
                                   xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>
            </fo:block-container>
        </fo:static-content>
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="auto" margin-top="0mm"  padding="6mm" padding-bottom="2mm"
                                keep-with-next="always" overflow="auto">

                <fo:table border="none">
                    <fo:table-column column-width="40mm" />
                    <fo:table-column column-width="190mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal454783' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="22pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt" th:if="${model.body.orgSlug} != 'del189476'">
                                    CAMBRIDGE INTERNATIONAL EDUCATION
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="7pt">
                                    LEARNING MILESTONES REPORT - 2024-25
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic th:src="${ model.body.orgSlug == 'del189476'? ' ' : 'url(https://images.wexledu.com/logo3.jpeg)'}"
                                                         content-width="45mm" content-height="non-uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" text-align="center">
                    <fo:table border="1pt solid black" space-before="18pt" table-layout="fixed" width="100%" th:each="table, iterStat : ${model.body.aoReport.aoTables}">
                        <fo:table-column column-width="202mm" />
                        <fo:table-column column-width="70mm" />
                        <fo:table-body>
                            <!-- Only one occurrence of CURRICULUM AREA -->
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" padding-left="3mm" text-align="left" number-columns-spanned="2">
                                    <fo:block color="maroon">
                                        CURRICULUM AREA <fo:inline th:text="${iterStat.index + 1}"/>:
                                        <fo:inline th:text="${#strings.toUpperCase(table.title)}"/>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <!-- Start of LEARNING STATEMENTS -->
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-left="3mm" font-weight="bold" text-align="left">
                                    <fo:block>LEARNING STATEMENTS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center" font-weight="bold">
                                    <fo:block>LEARNING LEVEL INDICATOR</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="aoDetails : ${table.aoDetails}" font-size="13pt">
                                <fo:table-cell border="1pt solid black" padding="0.5mm" padding-left="3mm" text-align="left">
                                    <fo:block th:text="${aoDetails.learningStatement}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-size="12pt" display-align="center" padding-bottom="-2mm">
                                    <fo:block height="10pt" line-height="10pt" text-align="center" padding-top="4mm" >
                                        <fo:instream-foreign-object width="10pt" height="10pt">
                                            <svg width="30%" height="30%" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="25%" cy="25%" r="25%"
                                                        th:attr="fill=${aoDetails != null and aoDetails.indicator == 'red' ? 'red' :
                                                        aoDetails != null and aoDetails.indicator == 'amber' ? 'orange' :
                                                        aoDetails != null and aoDetails.indicator == 'green' ? 'green' : 'white'}"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="0.5mm" padding-left="3mm" text-align="left" font-size="13pt" number-columns-spanned="2">
                                    <fo:block>Area of Strength:
                                        <fo:inline font-weight="normal" th:text="${table.aosValue}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="0.5mm" padding-left="3mm" text-align="left" font-size="13pt" number-columns-spanned="2">
                                    <fo:block>Area of Focus:
                                        <fo:inline font-weight="normal" th:text="${table.aofValue}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fifth page-->
    <fo:page-sequence master-reference="invoice" th:if="${model.body.orgSlug != 'del189476'}">
    <fo:flow flow-name="xsl-region-body">
    <!-- Content of the second page -->
    <fo:block-container width="100%" height="93%" margin-top="0mm"  border="2pt solid black" padding="6mm">
        <fo:block-container absolute-position="absolute" top="20%" left="50%" width="0%" height="0%">
            <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                <fo:instream-foreign-object content-width="300%" content-height="300%">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                        <defs>
                            <filter id="brightnessFilter1">
                                <feComponentTransfer>
                                    <feFuncR type="linear" slope="7"/>
                                    <feFuncG type="linear" slope="7"/>
                                    <feFuncB type="linear" slope="7"/>
                                </feComponentTransfer>
                            </filter>
                            <filter id="brightnessFilter2">
                                <feComponentTransfer>
                                    <feFuncR type="linear" slope="7.5"/>
                                    <feFuncG type="linear" slope="7.5"/>
                                    <feFuncB type="linear" slope="7.5"/>
                                </feComponentTransfer>
                            </filter>
                        </defs>
                        <image filter="url(#brightnessFilter1)"
                               x="0" y="0"
                               width="100%" height="100%"
                               th:if="${model.body.orgSlug == 'pal454783'}"
                               xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>

                        <image filter="url(#brightnessFilter2)"
                               x="0" y="0"
                               width="100%" height="100%"
                               th:if="${model.body.orgSlug != 'pal454783'}"
                               xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                    </svg>
                </fo:instream-foreign-object>
            </fo:block>
        </fo:block-container>
        <fo:block font-weight="bold" padding="1mm" padding-left="3mm" text-align="left" font-size="13pt"
                  font-family="Times New Roman, serif">
            Learning Statements Learning Level Indicator  :
        </fo:block>
        <fo:table table-layout="fixed" width="100%" border="none" font-family="Times New Roman, serif" >
            <fo:table-column column-width="10mm"/>
            <fo:table-column column-width="250mm"/>
            <fo:table-body>
                <fo:table-row>
                    <fo:table-cell border="none">
                        <fo:block>
                            <fo:instream-foreign-object width="20pt" height="20pt">
                                <svg width="30%" height="30%" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="25%" cy="25%" r="25%" fill="red"/>
                                </svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:table-cell>
                    <fo:table-cell padding-top="1.5mm">
                        <fo:block font-size="11pt" padding-left="5pt">Red - Evolving to Progressing</fo:block>
                    </fo:table-cell>
                </fo:table-row>
                <fo:table-row>
                    <fo:table-cell border="none">
                        <fo:block space-before="10pt">
                            <fo:instream-foreign-object width="20pt" height="20pt">
                                <svg width="30%" height="30%" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="25%" cy="25%" r="25%" fill="orange"/>
                                </svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:table-cell>
                    <fo:table-cell>
                        <fo:block font-size="11pt" padding-left="5pt" padding-top="1.5mm">Amber - Progressing to Prominent</fo:block>
                    </fo:table-cell>
                </fo:table-row>

                <fo:table-row>
                    <fo:table-cell border="none">
                        <fo:block space-before="10pt">
                            <fo:instream-foreign-object width="20pt" height="20pt">
                                <svg width="30%" height="30%" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="25%" cy="25%" r="25%" fill="green"/>
                                </svg>
                            </fo:instream-foreign-object>
                        </fo:block>
                    </fo:table-cell>
                    <fo:table-cell>
                        <fo:block font-size="11pt" padding-left="5pt" padding-top="1.5mm">Green - Confident to Skillfull</fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </fo:table-body>
        </fo:table>

    </fo:block-container>
    </fo:flow>
    </fo:page-sequence>

</fo:root>