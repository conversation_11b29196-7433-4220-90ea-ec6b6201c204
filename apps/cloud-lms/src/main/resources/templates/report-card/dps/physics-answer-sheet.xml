<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xi="http://www.w3.org/2001/XInclude"
         xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body  margin="12mm"  />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(1, 12)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="5mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
                <!--<fo:table table-layout="fixed" width="100%" border="1px solid black">
                    <fo:table-column column-width="6%"/>
                    <fo:table-column column-width="94%"/>
                    <fo:table-body>
                        <th:block th:each="i : ${#numbers.sequence(1, 30)}">
                            <fo:table-row height="8mm">
                                <fo:table-cell border="1px solid black" >
                                    <fo:block padding-top="2mm" text-align="center" th:if="${i >= 2 and i <= 11}" th:text="${i - 1}"></fo:block>
                                    <fo:block th:if="${i < 2 or i > 11}">&#160;</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1px solid black">
                                    <fo:block th:if="${i == 1}"   font-weight="bold">
                                        <fo:block padding-top="7mm">
                                            <fo:inline>Student name:</fo:inline>
                                            <fo:inline th:text="${model.body.name}"></fo:inline>
                                        </fo:block>
                                        <fo:block  padding-top="-4.8mm" margin-left="67%">
                                            <fo:inline>Roll number:</fo:inline>
                                            <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                                        </fo:block>
                                    </fo:block>
                                    <fo:block th:unless="${i == 1}">&#160;</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </th:block>
                    </fo:table-body>
                </fo:table>-->
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(13, 24)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="5mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--third-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(25, 27)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="60mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fourth-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(28, 30)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="60mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--fifth-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(31, 32)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--six-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(33, 34)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--seventh-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(35, 36)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--eight-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(37, 38)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <!--ningth-->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">
                <fo:block padding-top="0mm" font-weight="bold">
                    <fo:inline>Student name:</fo:inline>
                    <fo:inline th:text="${model.body.name}"></fo:inline>
                </fo:block>
                <fo:block  padding-top="-4.8mm" margin-left="70%" font-weight="bold" space-after="10mm">
                    <fo:inline>Roll number:</fo:inline>
                    <fo:inline space-before="5mm" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <th:block th:each="i : ${#numbers.sequence(39,39)}">
                    <fo:block>
                        <fo:block font-weight="bold" space-after="100mm">
                            <fo:inline  th:text="${'Question ' + (i)}"></fo:inline>
                        </fo:block>
                        <fo:block  font-size="10pt">
                            <fo:inline>END OF SEGMENT</fo:inline>
                        </fo:block>
                        <fo:block font-size="4pt" border-bottom="0.7pt solid black" space-after="4mm"></fo:block>
                    </fo:block>
                </th:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>